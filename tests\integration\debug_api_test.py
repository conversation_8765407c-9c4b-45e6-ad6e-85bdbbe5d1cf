"""
调试API测试
"""

import pytest
from fastapi.testclient import TestClient

from models.database import RuleTemplate, RuleFieldMetadata


def test_debug_api_response(client: TestClient, db_session):
    """调试API响应"""
    # 1. 创建规则模板
    template = RuleTemplate(
        rule_key="debug_test", rule_type="调试测试", name="调试API测试", description="调试API响应", status="READY"
    )
    db_session.add(template)
    db_session.commit()

    # 2. 测试创建请求
    create_data = {
        "rule_id": "DEBUG001",
        "rule_key": "debug_test",
        "rule_name": "调试测试明细",
        "level1": "调试",
        "level2": "测试",
        "level3": "API",
        "error_reason": "调试错误",
        "degree": "严重",
        "reference": "调试参考",
        "detail_position": "调试位置",
        "prompted_fields1": "debug_field",
        "type": "调试",
        "pos": "门诊",
        "applicableArea": "全国",
        "default_use": "是",
        "start_date": "2024-01-01",
        "end_date": "2024-12-31",
        # 添加必需的数组字段
        "yb_code": ["DEBUG001"],
        "diag_whole_code": [],
        "diag_code_prefix": [],
        "fee_whole_code": [],
        "fee_code_prefix": [],
        "status": "ACTIVE",
    }

    headers = {"X-API-KEY": "a_very_secret_key_for_development", "Content-Type": "application/json"}

    # 发送请求
    response = client.post(f"/api/v1/rules/details/{create_data['rule_key']}", json=create_data, headers=headers)

    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text}")

    # 验证响应
    assert response.status_code == 200
    response_data = response.json()
    print(f"Response Data: {response_data}")
