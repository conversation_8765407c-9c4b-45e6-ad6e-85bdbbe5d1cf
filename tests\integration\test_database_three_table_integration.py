"""
规则详情表三表关联集成测试

测试 rule_template、rule_detail、rule_field_metadata 三表之间的关联关系、
数据一致性约束、复杂查询性能等集成功能。

测试覆盖：
1. 三表外键关联关系
2. CASCADE删除操作
3. 数据一致性约束
4. 复杂联合查询
5. 事务处理
6. 数据完整性验证
"""

import pytest
from sqlalchemy import text
from sqlalchemy.exc import IntegrityError

from models.database import RuleTemplate, RuleDetail, RuleFieldMetadata


@pytest.mark.integration
@pytest.mark.database
class TestDatabaseThreeTableIntegration:
    """三表关联集成测试类"""

    def test_three_table_foreign_key_relationships(self, db_session):
        """测试三表外键关联关系"""
        # 1. 创建规则模板
        template = RuleTemplate(
            rule_key="test_drug_limit",
            rule_type="药品限制",
            name="测试药品限制规则",
            description="用于测试的药品限制规则模板",
            status="READY",
        )
        db_session.add(template)
        db_session.flush()  # 获取ID但不提交

        # 2. 创建字段元数据
        metadata1 = RuleFieldMetadata(
            rule_key="test_drug_limit",
            field_name="age_threshold",
            field_type="integer",
            is_required=True,
            display_name="年龄阈值",
            description="药品使用的年龄限制",
            validation_rule='{"min": 0, "max": 150}',
            excel_column_order=26,
        )

        metadata2 = RuleFieldMetadata(
            rule_key="test_drug_limit",
            field_name="yb_code",
            field_type="string",
            is_required=True,
            display_name="药品编码",
            description="医保药品编码",
            validation_rule='{"required": true}',
            excel_column_order=1,
        )

        db_session.add_all([metadata1, metadata2])
        db_session.flush()

        # 3. 创建规则明细
        detail = RuleDetail(
            rule_id="TEST001",
            rule_key="test_drug_limit",
            rule_name="测试规则明细",
            level1="药品管理",
            level2="适应症限制",
            level3="年龄限制",
            error_reason="患者年龄不符合药品使用要求",
            degree="严重",
            reference="医保目录管理办法",
            detail_position="处方审核",
            prompted_fields1="age,yb_code",
            type="药品限制",
            pos="门诊",
            applicableArea="全国",
            default_use="是",
            start_date="2024-01-01",
            end_date="2024-12-31",
            yb_code="XB05BA01,XB05BA02",
            extended_fields='{"age_threshold": 18, "limit_days": 30}',
            status="ACTIVE",
        )
        db_session.add(detail)
        db_session.commit()

        # 4. 验证关联关系
        # 通过模板查询明细
        template_with_details = db_session.query(RuleTemplate).filter_by(rule_key="test_drug_limit").first()
        assert template_with_details is not None
        assert len(template_with_details.rule_details) == 1
        assert template_with_details.rule_details[0].rule_name == "测试规则明细"

        # 通过模板查询字段元数据
        assert len(template_with_details.field_metadata) == 2
        field_names = [meta.field_name for meta in template_with_details.field_metadata]
        assert "age_threshold" in field_names
        assert "yb_code" in field_names

        # 通过明细查询模板
        detail_with_template = db_session.query(RuleDetail).filter_by(rule_id="TEST001").first()
        assert detail_with_template.template is not None
        assert detail_with_template.template.name == "测试药品限制规则"

    def test_cascade_delete_operations(self, db_session):
        """测试CASCADE删除操作"""
        # 1. 创建完整的三表数据
        template = RuleTemplate(
            rule_key="test_cascade",
            rule_type="测试类型",
            name="CASCADE删除测试",
            description="测试CASCADE删除功能",
            status="READY",
        )
        db_session.add(template)
        db_session.flush()

        # 创建多个字段元数据
        metadata_list = []
        for i in range(3):
            metadata = RuleFieldMetadata(
                rule_key="test_cascade",
                field_name=f"test_field_{i}",
                field_type="string",
                is_required=False,
                display_name=f"测试字段{i}",
                description=f"测试字段{i}的描述",
                excel_column_order=i + 1,
            )
            metadata_list.append(metadata)

        db_session.add_all(metadata_list)

        # 创建多个规则明细
        detail_list = []
        for i in range(2):
            detail = RuleDetail(
                rule_id=f"CASCADE{i:03d}",
                rule_key="test_cascade",
                rule_name=f"CASCADE测试明细{i}",
                level1="测试",
                level2="CASCADE",
                level3="删除",
                error_reason="测试错误原因",
                degree="轻微",
                reference="测试参考",
                detail_position="测试位置",
                prompted_fields1="test_field",
                type="测试",
                pos="测试",
                applicableArea="测试",
                default_use="否",
                start_date="2024-01-01",
                end_date="2024-12-31",
                status="ACTIVE",
            )
            detail_list.append(detail)

        db_session.add_all(detail_list)
        db_session.commit()

        # 2. 验证数据已创建
        assert db_session.query(RuleTemplate).filter_by(rule_key="test_cascade").count() == 1
        assert db_session.query(RuleFieldMetadata).filter_by(rule_key="test_cascade").count() == 3
        assert db_session.query(RuleDetail).filter_by(rule_key="test_cascade").count() == 2

        # 3. 删除模板，测试CASCADE删除
        template_to_delete = db_session.query(RuleTemplate).filter_by(rule_key="test_cascade").first()
        db_session.delete(template_to_delete)
        db_session.commit()

        # 4. 验证关联数据已被CASCADE删除
        assert db_session.query(RuleTemplate).filter_by(rule_key="test_cascade").count() == 0
        assert db_session.query(RuleFieldMetadata).filter_by(rule_key="test_cascade").count() == 0
        assert db_session.query(RuleDetail).filter_by(rule_key="test_cascade").count() == 0

    def test_data_integrity_constraints(self, db_session):
        """测试数据完整性约束"""
        # 1. 测试外键约束 - 不能创建没有对应模板的明细
        with pytest.raises(IntegrityError):
            detail = RuleDetail(
                rule_id="INVALID001",
                rule_key="non_existent_template",  # 不存在的模板
                rule_name="无效明细",
                level1="测试",
                level2="约束",
                level3="外键",
                error_reason="测试外键约束",
                degree="严重",
                reference="测试",
                detail_position="测试",
                prompted_fields1="test",
                type="测试",
                pos="测试",
                applicableArea="测试",
                default_use="否",
                start_date="2024-01-01",
                end_date="2024-12-31",
                status="ACTIVE",
            )
            db_session.add(detail)
            db_session.commit()

        # 回滚事务
        db_session.rollback()

        # 2. 测试外键约束 - 不能创建没有对应模板的字段元数据
        with pytest.raises(IntegrityError):
            metadata = RuleFieldMetadata(
                rule_key="non_existent_template",  # 不存在的模板
                field_name="invalid_field",
                field_type="string",
                is_required=False,
                display_name="无效字段",
                description="测试外键约束",
                excel_column_order=1,
            )
            db_session.add(metadata)
            db_session.commit()

        # 回滚事务
        db_session.rollback()

        # 3. 测试唯一约束 - rule_key在模板表中必须唯一
        template1 = RuleTemplate(
            rule_key="unique_test", rule_type="测试", name="唯一约束测试1", description="测试唯一约束", status="READY"
        )
        db_session.add(template1)
        db_session.commit()

        with pytest.raises(IntegrityError):
            template2 = RuleTemplate(
                rule_key="unique_test",  # 重复的rule_key
                rule_type="测试",
                name="唯一约束测试2",
                description="测试唯一约束",
                status="READY",
            )
            db_session.add(template2)
            db_session.commit()

    def test_complex_join_queries(self, db_session):
        """测试复杂联合查询"""
        # 1. 创建测试数据
        # 创建两个模板
        template1 = RuleTemplate(
            rule_key="complex_query_1",
            rule_type="药品限制",
            name="复杂查询测试1",
            description="测试复杂查询功能",
            status="READY",
        )

        template2 = RuleTemplate(
            rule_key="complex_query_2",
            rule_type="诊断限制",
            name="复杂查询测试2",
            description="测试复杂查询功能",
            status="READY",
        )

        db_session.add_all([template1, template2])
        db_session.flush()

        # 为每个模板创建字段元数据
        metadata_list = []
        for i, rule_key in enumerate(["complex_query_1", "complex_query_2"]):
            for j in range(2):
                metadata = RuleFieldMetadata(
                    rule_key=rule_key,
                    field_name=f"field_{i}_{j}",
                    field_type="string",
                    is_required=j == 0,  # 第一个字段必填
                    display_name=f"字段{i}_{j}",
                    description=f"模板{i}的字段{j}",
                    excel_column_order=j + 1,
                )
                metadata_list.append(metadata)

        db_session.add_all(metadata_list)

        # 为每个模板创建规则明细
        detail_list = []
        for i, rule_key in enumerate(["complex_query_1", "complex_query_2"]):
            for j in range(3):  # 每个模板3个明细
                detail = RuleDetail(
                    rule_id=f"COMPLEX{i}{j:02d}",
                    rule_key=rule_key,
                    rule_name=f"复杂查询明细{i}_{j}",
                    level1=f"级别1_{i}",
                    level2=f"级别2_{j}",
                    level3="级别3",
                    error_reason=f"错误原因{i}_{j}",
                    degree="严重" if j % 2 == 0 else "轻微",
                    reference=f"参考{i}",
                    detail_position=f"位置{j}",
                    prompted_fields1=f"field_{i}_0",
                    type=f"类型{i}",
                    pos="门诊" if i == 0 else "住院",
                    applicableArea="全国",
                    default_use="是" if j == 0 else "否",
                    start_date="2024-01-01",
                    end_date="2024-12-31",
                    status="ACTIVE",
                )
                detail_list.append(detail)

        db_session.add_all(detail_list)
        db_session.commit()

        # 2. 测试复杂联合查询
        # 查询所有药品限制类型的模板及其明细和字段元数据
        query_result = (
            db_session.query(RuleTemplate, RuleDetail, RuleFieldMetadata)
            .join(RuleDetail, RuleTemplate.rule_key == RuleDetail.rule_key)
            .join(RuleFieldMetadata, RuleTemplate.rule_key == RuleFieldMetadata.rule_key)
            .filter(RuleTemplate.rule_type == "药品限制")
            .all()
        )

        # 验证查询结果
        assert len(query_result) == 6  # 1个模板 × 3个明细 × 2个字段元数据 = 6条记录

        # 验证所有记录都属于药品限制类型
        for template, detail, metadata in query_result:
            assert template.rule_type == "药品限制"
            assert template.rule_key == "complex_query_1"
            assert detail.rule_key == "complex_query_1"
            assert metadata.rule_key == "complex_query_1"

        # 3. 测试聚合查询
        # 统计每个模板的明细数量和字段数量
        from sqlalchemy import func

        stats_query = (
            db_session.query(
                RuleTemplate.rule_key,
                RuleTemplate.name,
                func.count(RuleDetail.id.distinct()).label("detail_count"),
                func.count(RuleFieldMetadata.id.distinct()).label("field_count"),
            )
            .outerjoin(RuleDetail, RuleTemplate.rule_key == RuleDetail.rule_key)
            .outerjoin(RuleFieldMetadata, RuleTemplate.rule_key == RuleFieldMetadata.rule_key)
            .group_by(RuleTemplate.rule_key, RuleTemplate.name)
            .all()
        )

        # 验证统计结果
        assert len(stats_query) == 2  # 两个模板

        for rule_key, name, detail_count, field_count in stats_query:
            assert detail_count == 3  # 每个模板3个明细
            assert field_count == 2  # 每个模板2个字段

    def test_transaction_rollback(self, db_session):
        """测试事务回滚功能"""
        # 1. 记录初始状态
        initial_template_count = db_session.query(RuleTemplate).count()
        initial_detail_count = db_session.query(RuleDetail).count()
        initial_metadata_count = db_session.query(RuleFieldMetadata).count()

        try:
            # 2. 开始事务操作
            template = RuleTemplate(
                rule_key="transaction_test",
                rule_type="事务测试",
                name="事务回滚测试",
                description="测试事务回滚功能",
                status="READY",
            )
            db_session.add(template)
            db_session.flush()

            metadata = RuleFieldMetadata(
                rule_key="transaction_test",
                field_name="test_field",
                field_type="string",
                is_required=True,
                display_name="测试字段",
                description="事务测试字段",
                excel_column_order=1,
            )
            db_session.add(metadata)
            db_session.flush()

            detail = RuleDetail(
                rule_id="TRANS001",
                rule_key="transaction_test",
                rule_name="事务测试明细",
                level1="事务",
                level2="测试",
                level3="回滚",
                error_reason="事务测试错误",
                degree="严重",
                reference="事务测试",
                detail_position="测试",
                prompted_fields1="test_field",
                type="事务测试",
                pos="测试",
                applicableArea="测试",
                default_use="否",
                start_date="2024-01-01",
                end_date="2024-12-31",
                status="ACTIVE",
            )
            db_session.add(detail)
            db_session.flush()

            # 3. 验证数据已添加（但未提交）
            assert db_session.query(RuleTemplate).filter_by(rule_key="transaction_test").count() == 1
            assert db_session.query(RuleFieldMetadata).filter_by(rule_key="transaction_test").count() == 1
            assert db_session.query(RuleDetail).filter_by(rule_key="transaction_test").count() == 1

            # 4. 故意触发错误以测试回滚
            # 尝试创建重复的rule_key（违反唯一约束）
            duplicate_template = RuleTemplate(
                rule_key="transaction_test",  # 重复的rule_key
                rule_type="重复测试",
                name="重复模板",
                description="测试重复",
                status="READY",
            )
            db_session.add(duplicate_template)
            db_session.commit()  # 这里会触发IntegrityError

        except IntegrityError:
            # 5. 回滚事务
            db_session.rollback()

        # 6. 验证回滚后数据状态
        assert db_session.query(RuleTemplate).count() == initial_template_count
        assert db_session.query(RuleDetail).count() == initial_detail_count
        assert db_session.query(RuleFieldMetadata).count() == initial_metadata_count

        # 验证测试数据已被回滚
        assert db_session.query(RuleTemplate).filter_by(rule_key="transaction_test").count() == 0
        assert db_session.query(RuleFieldMetadata).filter_by(rule_key="transaction_test").count() == 0
        assert db_session.query(RuleDetail).filter_by(rule_key="transaction_test").count() == 0
