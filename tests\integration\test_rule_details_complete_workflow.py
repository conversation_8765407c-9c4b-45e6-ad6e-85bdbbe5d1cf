"""
规则详情表完整业务流程集成测试

测试规则详情表重构后的完整业务流程，从规则模板创建到Excel模板生成，
验证整个业务链路的数据流转和状态变化。

测试覆盖：
1. 规则模板创建 → 字段元数据初始化 → 规则明细CRUD
2. Excel模板生成流程
3. 数据校验流程
4. 批量操作流程
5. 状态变更流程
6. 完整的端到端业务场景
"""

import pytest
import json
from fastapi.testclient import TestClient

from models.database import RuleTemplate, RuleDetail, RuleFieldMetadata
from services.rule_field_metadata_initializer import RuleFieldMetadataInitializer


@pytest.mark.integration
@pytest.mark.workflow
@pytest.mark.slow
class TestRuleDetailsCompleteWorkflow:
    """完整业务流程集成测试类"""

    def setup_method(self, method):
        """每个测试方法前的设置"""
        self.api_headers = {"X-API-KEY": "a_very_secret_key_for_development", "Content-Type": "application/json"}

    def test_complete_rule_lifecycle_workflow(self, client: TestClient, db_session):
        """测试完整的规则生命周期工作流程"""

        # ============================================================================
        # 阶段1：规则模板创建和字段元数据初始化
        # ============================================================================

        # 1.1 创建规则模板
        template = RuleTemplate(
            rule_key="workflow_test_drug_limit",
            rule_type="药品限制",
            name="工作流程测试-药品限制规则",
            description="测试完整工作流程的药品限制规则模板",
            module_path="rules.drug_limit_adult_and_diag_exact",
            status="NEW",
        )
        db_session.add(template)
        db_session.commit()

        # 1.2 初始化字段元数据
        initializer = RuleFieldMetadataInitializer(db_session)

        # 手动创建字段元数据（模拟初始化过程）
        metadata_list = [
            RuleFieldMetadata(
                rule_key="workflow_test_drug_limit",
                field_name="age_threshold",
                field_type="integer",
                is_required=True,
                display_name="年龄阈值",
                description="药品使用的年龄限制",
                validation_rule='{"min": 0, "max": 150}',
                excel_column_order=26,
            ),
            RuleFieldMetadata(
                rule_key="workflow_test_drug_limit",
                field_name="limit_days",
                field_type="integer",
                is_required=False,
                display_name="限制天数",
                description="药品使用的天数限制",
                validation_rule='{"min": 1, "max": 365}',
                excel_column_order=27,
            ),
            RuleFieldMetadata(
                rule_key="workflow_test_drug_limit",
                field_name="institution_levels",
                field_type="array",
                is_required=False,
                display_name="机构等级",
                description="适用的医疗机构等级",
                validation_rule='{"items": ["一级", "二级", "三级"]}',
                excel_column_order=28,
            ),
        ]

        db_session.add_all(metadata_list)
        db_session.commit()

        # 1.3 验证模板和元数据创建成功
        created_template = db_session.query(RuleTemplate).filter_by(rule_key="workflow_test_drug_limit").first()
        assert created_template is not None
        assert created_template.status == "NEW"
        assert len(created_template.field_metadata) == 3

        # ============================================================================
        # 阶段2：规则明细CRUD操作
        # ============================================================================

        # 2.1 创建规则明细
        create_data = {
            "rule_id": "WORKFLOW001",
            "rule_key": "workflow_test_drug_limit",
            "rule_name": "工作流程测试规则明细",
            "level1": "药品管理",
            "level2": "适应症限制",
            "level3": "年龄限制",
            "error_reason": "患者年龄不符合药品使用要求",
            "degree": "严重",
            "reference": "医保目录管理办法第XX条",
            "detail_position": "处方审核环节",
            "prompted_fields1": "age,yb_code",
            "type": "药品限制",
            "pos": "门诊",
            "applicableArea": "全国",
            "default_use": "是",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            # 修正数组字段格式
            "yb_code": ["XB05BA01", "XB05BA02"],
            "diag_whole_code": [],
            "diag_code_prefix": [],
            "fee_whole_code": [],
            "fee_code_prefix": [],
            "extended_fields": '{"age_threshold": 18, "limit_days": 30, "institution_levels": ["二级", "三级"]}',
            "status": "ACTIVE",
        }

        # 发送创建请求
        create_response = client.post(
            f"/api/v1/rules/details/{create_data['rule_key']}", json=create_data, headers=self.api_headers
        )

        assert create_response.status_code == 200
        create_result = create_response.json()
        assert create_result["success"] is True

        created_detail = create_result["data"]
        detail_id = created_detail["id"]

        # 2.2 查询规则明细
        query_response = client.get(f"/api/v1/rules/details/workflow_test_drug_limit", headers=self.api_headers)

        assert query_response.status_code == 200
        query_result = query_response.json()
        assert query_result["success"] is True
        assert len(query_result["data"]) == 1

        queried_detail = query_result["data"][0]
        assert queried_detail["rule_id"] == "WORKFLOW001"
        assert queried_detail["rule_name"] == "工作流程测试规则明细"

        # 2.3 更新规则明细
        update_data = {
            "rule_name": "工作流程测试规则明细（已更新）",
            "degree": "轻微",
            "extended_fields": '{"age_threshold": 16, "limit_days": 45, "institution_levels": ["一级", "二级", "三级"]}',
        }

        update_response = client.put(
            f"/api/v1/rules/details/workflow_test_drug_limit/{detail_id}", json=update_data, headers=self.api_headers
        )

        assert update_response.status_code == 200
        update_result = update_response.json()
        assert update_result["success"] is True

        updated_detail = update_result["data"]
        assert updated_detail["rule_name"] == "工作流程测试规则明细（已更新）"
        assert updated_detail["degree"] == "轻微"

        # 验证扩展字段更新
        if isinstance(updated_detail.get("extended_fields"), str):
            extended_fields = json.loads(updated_detail["extended_fields"])
        else:
            extended_fields = updated_detail.get("extended_fields", {})

        assert extended_fields["age_threshold"] == 16
        assert extended_fields["limit_days"] == 45
        assert len(extended_fields["institution_levels"]) == 3

        # ============================================================================
        # 阶段3：批量操作流程
        # ============================================================================

        # 3.1 批量创建规则明细
        batch_data = []
        for i in range(3):
            detail_data = {
                "rule_id": f"WORKFLOW{i + 2:03d}",
                "rule_key": "workflow_test_drug_limit",
                "rule_name": f"批量创建明细{i + 1}",
                "level1": "药品管理",
                "level2": "批量操作",
                "level3": f"明细{i + 1}",
                "error_reason": f"批量测试错误{i + 1}",
                "degree": "严重" if i % 2 == 0 else "轻微",
                "reference": "批量操作测试参考",
                "detail_position": f"批量位置{i + 1}",
                "prompted_fields1": "batch_field",
                "type": "药品限制",
                "pos": "门诊",
                "applicableArea": "全国",
                "default_use": "否",
                "start_date": "2024-01-01",
                "end_date": "2024-12-31",
                # 添加必需的数组字段
                "yb_code": [f"WORKFLOW{i + 2:03d}"],
                "diag_whole_code": [],
                "diag_code_prefix": [],
                "fee_whole_code": [],
                "fee_code_prefix": [],
                "extended_fields": f'{{"age_threshold": {20 + i * 5}, "limit_days": {30 + i * 10}, "batch_index": {i}}}',
                "status": "ACTIVE",
            }
            batch_data.append(detail_data)

        batch_response = client.post(
            "/api/v1/rules/details/workflow_test_drug_limit/batch",
            json={"details": batch_data},
            headers=self.api_headers,
        )

        assert batch_response.status_code == 200
        batch_result = batch_response.json()
        assert batch_result["success"] is True
        assert len(batch_result["data"]) == 3

        # 3.2 验证批量创建结果
        final_query_response = client.get(f"/api/v1/rules/details/workflow_test_drug_limit", headers=self.api_headers)

        assert final_query_response.status_code == 200
        final_query_result = final_query_response.json()
        assert final_query_result["success"] is True
        assert len(final_query_result["data"]) == 4  # 1个原始 + 3个批量创建

        # ============================================================================
        # 阶段4：状态变更和生命周期管理
        # ============================================================================

        # 4.1 更新模板状态
        template.status = "READY"
        db_session.commit()

        # 4.2 测试规则明细状态变更
        # 将第一个明细设置为INACTIVE
        status_update_data = {"status": "INACTIVE"}

        status_response = client.put(
            f"/api/v1/rules/details/workflow_test_drug_limit/{detail_id}",
            json=status_update_data,
            headers=self.api_headers,
        )

        assert status_response.status_code == 200
        status_result = status_response.json()
        assert status_result["success"] is True
        assert status_result["data"]["status"] == "INACTIVE"

        # ============================================================================
        # 阶段5：数据完整性验证
        # ============================================================================

        # 5.1 验证数据库中的数据一致性
        # 验证模板
        final_template = db_session.query(RuleTemplate).filter_by(rule_key="workflow_test_drug_limit").first()
        assert final_template.status == "READY"
        assert len(final_template.rule_details) == 4
        assert len(final_template.field_metadata) == 3

        # 验证明细状态
        active_details = (
            db_session.query(RuleDetail).filter_by(rule_key="workflow_test_drug_limit", status="ACTIVE").count()
        )
        inactive_details = (
            db_session.query(RuleDetail).filter_by(rule_key="workflow_test_drug_limit", status="INACTIVE").count()
        )

        assert active_details == 3  # 3个ACTIVE
        assert inactive_details == 1  # 1个INACTIVE

        # 5.2 验证扩展字段数据完整性
        all_details = db_session.query(RuleDetail).filter_by(rule_key="workflow_test_drug_limit").all()

        for detail in all_details:
            if detail.extended_fields:
                extended_data = json.loads(detail.extended_fields)
                # 验证必要的扩展字段存在
                assert "age_threshold" in extended_data
                assert isinstance(extended_data["age_threshold"], int)
                assert extended_data["age_threshold"] >= 0

    def test_error_recovery_workflow(self, client: TestClient, db_session):
        """测试错误恢复工作流程"""

        # 1. 创建基础模板
        template = RuleTemplate(
            rule_key="error_recovery_test",
            rule_type="错误恢复测试",
            name="错误恢复测试模板",
            description="测试错误恢复功能",
            status="READY",
        )
        db_session.add(template)
        db_session.commit()

        # 2. 测试创建无效数据的错误恢复
        invalid_data = {
            "rule_id": "ERROR001",
            "rule_key": "error_recovery_test",
            # 缺少必填字段 rule_name
            "level1": "错误测试",
            "level2": "恢复测试",
            "level3": "无效数据",
            "error_reason": "错误恢复测试",
            "degree": "严重",
            "reference": "错误测试参考",
            "detail_position": "错误位置",
            "prompted_fields1": "error_field",
            "type": "错误测试",
            "pos": "门诊",
            "applicableArea": "全国",
            "default_use": "否",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            # 添加必需的数组字段
            "yb_code": ["ERROR001"],
            "diag_whole_code": [],
            "diag_code_prefix": [],
            "fee_whole_code": [],
            "fee_code_prefix": [],
            "status": "ACTIVE",
        }

        # 发送无效请求
        error_response = client.post(
            f"/api/v1/rules/details/{invalid_data['rule_key']}", json=invalid_data, headers=self.api_headers
        )

        # 验证错误响应
        assert error_response.status_code == 200  # 统一返回200
        error_result = error_response.json()
        assert error_result["success"] is False

        # 3. 验证数据库状态未被破坏
        detail_count = db_session.query(RuleDetail).filter_by(rule_key="error_recovery_test").count()
        assert detail_count == 0  # 没有创建任何明细

        # 4. 修正数据后重新创建
        valid_data = invalid_data.copy()
        valid_data["rule_name"] = "错误恢复测试明细"

        success_response = client.post(
            f"/api/v1/rules/details/{valid_data['rule_key']}", json=valid_data, headers=self.api_headers
        )

        assert success_response.status_code == 200
        success_result = success_response.json()
        assert success_result["success"] is True

        # 5. 验证恢复后的数据正确性
        final_count = db_session.query(RuleDetail).filter_by(rule_key="error_recovery_test").count()
        assert final_count == 1
