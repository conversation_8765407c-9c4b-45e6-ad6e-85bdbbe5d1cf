"""
API字段映射集成测试

测试API接口的字段映射转换功能，确保前后端字段命名一致性，
验证UnifiedDataMappingEngine在API层的集成效果。

测试覆盖：
1. API请求/响应字段映射转换
2. 前后端字段命名一致性
3. 扩展字段JSON序列化/反序列化
4. 字段验证和错误处理
5. 批量操作字段映射
6. 数据格式兼容性
"""

import pytest
import json
from fastapi.testclient import TestClient

from models.database import RuleTemplate, RuleDetail, RuleFieldMetadata


@pytest.mark.integration
@pytest.mark.api
@pytest.mark.field_mapping
class TestApiFieldMappingIntegration:
    """API字段映射集成测试类"""

    def setup_method(self, method):
        """每个测试方法前的设置"""
        self.api_headers = {"X-API-KEY": "a_very_secret_key_for_development", "Content-Type": "application/json"}

    def test_api_request_field_mapping(self, client: TestClient, db_session):
        """测试API请求字段映射转换"""
        # 1. 先创建规则模板和字段元数据
        template = RuleTemplate(
            rule_key="api_mapping_test",
            rule_type="API测试",
            name="API字段映射测试",
            description="测试API字段映射功能",
            status="READY",
        )
        db_session.add(template)

        metadata = RuleFieldMetadata(
            rule_key="api_mapping_test",
            field_name="age_threshold",
            field_type="integer",
            is_required=False,
            display_name="年龄阈值",
            description="年龄限制阈值",
            excel_column_order=26,
        )
        db_session.add(metadata)
        db_session.commit()

        # 2. 测试创建规则明细的字段映射
        # 使用标准字段名发送请求
        create_data = {
            "rule_id": "API001",
            "rule_key": "api_mapping_test",
            "rule_name": "API字段映射测试明细",
            "level1": "API测试",  # 标准字段名
            "level2": "字段映射",  # 标准字段名
            "level3": "转换测试",  # 标准字段名
            "error_reason": "API字段映射测试错误",
            "degree": "严重",
            "reference": "API测试参考",
            "detail_position": "API测试位置",
            "prompted_fields1": "age_threshold",
            "type": "API测试",
            "pos": "门诊",
            "applicableArea": "全国",
            "default_use": "是",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            # 修正数组字段格式
            "yb_code": ["API001", "API002"],
            "diag_whole_code": [],
            "diag_code_prefix": [],
            "fee_whole_code": [],
            "fee_code_prefix": [],
            "extended_fields": '{"age_threshold": 18, "limit_days": 30}',
            "status": "ACTIVE",
        }

        # 发送创建请求
        response = client.post(
            f"/api/v1/rules/details/{create_data['rule_key']}", json=create_data, headers=self.api_headers
        )

        # 3. 验证响应
        assert response.status_code == 200
        response_data = response.json()

        # 调试输出
        print(f"Response: {response_data}")

        assert response_data["success"] is True
        assert "data" in response_data

        created_detail = response_data["data"]

        # 验证字段映射正确
        assert created_detail["rule_id"] == "API001"
        assert created_detail["level1"] == "API测试"  # 标准字段名
        assert created_detail["level2"] == "字段映射"  # 标准字段名
        assert created_detail["level3"] == "转换测试"  # 标准字段名

        # 验证扩展字段正确处理
        if isinstance(created_detail.get("extended_fields"), str):
            extended_fields = json.loads(created_detail["extended_fields"])
        else:
            extended_fields = created_detail.get("extended_fields", {})

        assert extended_fields.get("age_threshold") == 18
        assert extended_fields.get("limit_days") == 30

    def test_api_response_field_mapping(self, client: TestClient, db_session):
        """测试API响应字段映射转换"""
        # 1. 直接在数据库中创建测试数据
        template = RuleTemplate(
            rule_key="response_mapping_test",
            rule_type="响应测试",
            name="响应字段映射测试",
            description="测试响应字段映射功能",
            status="READY",
        )
        db_session.add(template)

        detail = RuleDetail(
            rule_id="RESP001",
            rule_key="response_mapping_test",
            rule_name="响应映射测试明细",
            level1="响应测试",  # 数据库中使用标准字段名
            level2="字段映射",
            level3="API响应",
            error_reason="响应字段映射测试",
            degree="轻微",
            reference="响应测试参考",
            detail_position="响应测试位置",
            prompted_fields1="test_field",
            type="响应测试",
            pos="住院",
            applicableArea="全国",
            default_use="否",
            start_date="2024-01-01",
            end_date="2024-12-31",
            yb_code="RESP001,RESP002",
            extended_fields='{"response_test": true, "test_value": 100}',
            status="ACTIVE",
        )
        db_session.add(detail)
        db_session.commit()

        # 2. 通过API查询数据
        response = client.get(f"/api/v1/rules/details/response_mapping_test", headers=self.api_headers)

        # 3. 验证响应字段映射
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success"] is True
        assert "data" in response_data

        details_list = response_data["data"]
        assert len(details_list) == 1

        detail_data = details_list[0]

        # 验证标准字段名在响应中正确返回
        assert detail_data["rule_id"] == "RESP001"
        assert detail_data["level1"] == "响应测试"  # 标准字段名
        assert detail_data["level2"] == "字段映射"  # 标准字段名
        assert detail_data["level3"] == "API响应"  # 标准字段名

        # 验证扩展字段正确解析
        if isinstance(detail_data.get("extended_fields"), str):
            extended_fields = json.loads(detail_data["extended_fields"])
        else:
            extended_fields = detail_data.get("extended_fields", {})

        assert extended_fields.get("response_test") is True
        assert extended_fields.get("test_value") == 100

    def test_batch_operation_field_mapping(self, client: TestClient, db_session):
        """测试批量操作字段映射"""
        # 1. 创建规则模板
        template = RuleTemplate(
            rule_key="batch_mapping_test",
            rule_type="批量测试",
            name="批量字段映射测试",
            description="测试批量操作字段映射",
            status="READY",
        )
        db_session.add(template)
        db_session.commit()

        # 2. 准备批量创建数据
        batch_data = []
        for i in range(3):
            detail_data = {
                "rule_id": f"BATCH{i:03d}",
                "rule_key": "batch_mapping_test",
                "rule_name": f"批量测试明细{i}",
                "level1": f"批量测试{i}",  # 标准字段名
                "level2": "字段映射",
                "level3": "批量操作",
                "error_reason": f"批量测试错误{i}",
                "degree": "严重" if i % 2 == 0 else "轻微",
                "reference": "批量测试参考",
                "detail_position": f"批量位置{i}",
                "prompted_fields1": "batch_field",
                "type": "批量测试",
                "pos": "门诊",
                "applicableArea": "全国",
                "default_use": "是" if i == 0 else "否",
                "start_date": "2024-01-01",
                "end_date": "2024-12-31",
                # 添加必需的数组字段
                "yb_code": [f"BATCH{i:03d}"],
                "diag_whole_code": [],
                "diag_code_prefix": [],
                "fee_whole_code": [],
                "fee_code_prefix": [],
                "extended_fields": f'{{"batch_index": {i}, "batch_test": true}}',
                "status": "ACTIVE",
            }
            batch_data.append(detail_data)

        # 3. 发送批量创建请求
        response = client.post(
            f"/api/v1/rules/details/batch_mapping_test/batch", json={"details": batch_data}, headers=self.api_headers
        )

        # 4. 验证批量创建响应
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success"] is True
        assert "data" in response_data

        created_details = response_data["data"]
        assert len(created_details) == 3

        # 验证每个创建的明细字段映射正确
        for i, detail in enumerate(created_details):
            assert detail["rule_id"] == f"BATCH{i:03d}"
            assert detail["level1"] == f"批量测试{i}"  # 标准字段名
            assert detail["level2"] == "字段映射"
            assert detail["level3"] == "批量操作"

            # 验证扩展字段
            if isinstance(detail.get("extended_fields"), str):
                extended_fields = json.loads(detail["extended_fields"])
            else:
                extended_fields = detail.get("extended_fields", {})

            assert extended_fields.get("batch_index") == i
            assert extended_fields.get("batch_test") is True

    def test_field_validation_error_handling(self, client: TestClient, db_session):
        """测试字段验证和错误处理"""
        # 1. 创建规则模板
        template = RuleTemplate(
            rule_key="validation_test",
            rule_type="验证测试",
            name="字段验证测试",
            description="测试字段验证功能",
            status="READY",
        )
        db_session.add(template)
        db_session.commit()

        # 2. 测试必填字段缺失
        invalid_data = {
            "rule_id": "INVALID001",
            "rule_key": "validation_test",
            # 缺少必填字段 rule_name
            "level1": "验证测试",
            "level2": "错误处理",
            "level3": "字段验证",
            "error_reason": "验证测试错误",
            "degree": "严重",
            "reference": "验证测试参考",
            "detail_position": "验证位置",
            "prompted_fields1": "test_field",
            "type": "验证测试",
            "pos": "门诊",
            "applicableArea": "全国",
            "default_use": "是",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            # 添加必需的数组字段
            "yb_code": ["INVALID001"],
            "diag_whole_code": [],
            "diag_code_prefix": [],
            "fee_whole_code": [],
            "fee_code_prefix": [],
            "status": "ACTIVE",
        }

        response = client.post(
            f"/api/v1/rules/details/{invalid_data['rule_key']}", json=invalid_data, headers=self.api_headers
        )

        # 3. 验证错误响应
        assert response.status_code == 200  # 统一返回200
        response_data = response.json()
        assert response_data["success"] is False
        assert "message" in response_data
        # 错误信息应该提到缺少必填字段
        assert "rule_name" in response_data["message"] or "必填" in response_data["message"]

    def test_extended_fields_json_handling(self, client: TestClient, db_session):
        """测试扩展字段JSON处理"""
        # 1. 创建规则模板和字段元数据
        template = RuleTemplate(
            rule_key="json_test",
            rule_type="JSON测试",
            name="JSON字段测试",
            description="测试JSON字段处理",
            status="READY",
        )
        db_session.add(template)

        # 创建多个扩展字段元数据
        metadata_list = [
            RuleFieldMetadata(
                rule_key="json_test",
                field_name="complex_object",
                field_type="string",
                is_required=False,
                display_name="复杂对象",
                description="复杂JSON对象",
                excel_column_order=26,
            ),
            RuleFieldMetadata(
                rule_key="json_test",
                field_name="number_array",
                field_type="array",
                is_required=False,
                display_name="数字数组",
                description="数字数组字段",
                excel_column_order=27,
            ),
        ]
        db_session.add_all(metadata_list)
        db_session.commit()

        # 2. 测试复杂扩展字段
        complex_extended_fields = {
            "complex_object": {
                "nested": {"value": 123, "text": "测试文本", "boolean": True},
                "array": [1, 2, 3, "test"],
            },
            "number_array": [10, 20, 30, 40, 50],
            "simple_string": "简单字符串",
            "simple_number": 42,
            "simple_boolean": False,
        }

        create_data = {
            "rule_id": "JSON001",
            "rule_key": "json_test",
            "rule_name": "JSON测试明细",
            "level1": "JSON测试",
            "level2": "扩展字段",
            "level3": "复杂对象",
            "error_reason": "JSON测试错误",
            "degree": "轻微",
            "reference": "JSON测试参考",
            "detail_position": "JSON位置",
            "prompted_fields1": "complex_object",
            "type": "JSON测试",
            "pos": "门诊",
            "applicableArea": "全国",
            "default_use": "否",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            # 添加必需的数组字段
            "yb_code": ["JSON001"],
            "diag_whole_code": [],
            "diag_code_prefix": [],
            "fee_whole_code": [],
            "fee_code_prefix": [],
            "extended_fields": json.dumps(complex_extended_fields),
            "status": "ACTIVE",
        }

        # 3. 发送创建请求
        response = client.post(
            f"/api/v1/rules/details/{create_data['rule_key']}", json=create_data, headers=self.api_headers
        )

        # 4. 验证创建响应
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success"] is True

        created_detail = response_data["data"]

        # 验证扩展字段正确处理
        if isinstance(created_detail.get("extended_fields"), str):
            returned_extended_fields = json.loads(created_detail["extended_fields"])
        else:
            returned_extended_fields = created_detail.get("extended_fields", {})

        # 验证复杂对象
        assert "complex_object" in returned_extended_fields
        complex_obj = returned_extended_fields["complex_object"]
        assert complex_obj["nested"]["value"] == 123
        assert complex_obj["nested"]["text"] == "测试文本"
        assert complex_obj["nested"]["boolean"] is True
        assert complex_obj["array"] == [1, 2, 3, "test"]

        # 验证数组
        assert returned_extended_fields["number_array"] == [10, 20, 30, 40, 50]

        # 验证简单类型
        assert returned_extended_fields["simple_string"] == "简单字符串"
        assert returned_extended_fields["simple_number"] == 42
        assert returned_extended_fields["simple_boolean"] is False

        # 5. 通过查询API验证数据持久化
        query_response = client.get(f"/api/v1/rules/details/json_test", headers=self.api_headers)

        assert query_response.status_code == 200
        query_data = query_response.json()
        assert query_data["success"] is True

        queried_detail = query_data["data"][0]

        # 验证查询返回的扩展字段与创建时一致
        if isinstance(queried_detail.get("extended_fields"), str):
            queried_extended_fields = json.loads(queried_detail["extended_fields"])
        else:
            queried_extended_fields = queried_detail.get("extended_fields", {})

        assert queried_extended_fields == complex_extended_fields
